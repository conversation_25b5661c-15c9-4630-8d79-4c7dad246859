{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AxisTestZ", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "AxisTestZ::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestZ-Debug-1ca3a69beb4958018e37.json", "name": "AxisTestZ", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisTestZ_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestZ_autogen-Debug-44b1845d3f34fc9968a3.json", "name": "AxisTestZ_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisTestZ_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestZ_autogen_timestamp_deps-Debug-ed18dea157387c70269a.json", "name": "AxisTestZ_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestZ/build", "source": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestZ"}, "version": {"major": 2, "minor": 7}}