{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AxisTestY", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "AxisTestY::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestY-Debug-7740fe77f54fb92e5ae4.json", "name": "AxisTestY", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisTestY_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestY_autogen-Debug-6b42a4f0121e537938ce.json", "name": "AxisTestY_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisTestY_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestY_autogen_timestamp_deps-Debug-0c464ed4dceeba37a0e0.json", "name": "AxisTestY_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build", "source": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY"}, "version": {"major": 2, "minor": 7}}