<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AxisTestZ</class>
 <widget class="QMainWindow" name="AxisTestZ">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Z轴测试程序</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QGroupBox" name="connectionGroup">
      <property name="title">
       <string>连接设置</string>
      </property>
      <layout class="QHBoxLayout" name="connectionLayout">
       <item>
        <widget class="QLabel" name="portLabel">
         <property name="text">
          <string>串口:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="portCombo"/>
       </item>
       <item>
        <widget class="QPushButton" name="connectBtn">
         <property name="text">
          <string>连接</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="disconnectBtn">
         <property name="text">
          <string>断开</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="connectionStatus">
         <property name="text">
          <string>未连接</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="axisControlGroup">
      <property name="title">
       <string>Z轴控制</string>
      </property>
      <layout class="QHBoxLayout" name="axisControlLayout">
       <item>
        <widget class="QPushButton" name="enableAxisBtn">
         <property name="text">
          <string>使能轴</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="disableAxisBtn">
         <property name="text">
          <string>失能轴</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="emergencyStopBtn">
         <property name="text">
          <string>急停</string>
         </property>
         <property name="styleSheet">
          <string>QPushButton { background-color: red; color: white; font-weight: bold; }</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="axisStatus">
         <property name="text">
          <string>状态: 未知</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="positionGroup">
      <property name="title">
       <string>位置信息</string>
      </property>
      <layout class="QGridLayout" name="positionLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="currentPosTextLabel">
         <property name="text">
          <string>当前位置:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLabel" name="currentPosLabel">
         <property name="text">
          <string>0.000 mm</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QLabel" name="velocityTextLabel">
         <property name="text">
          <string>当前速度:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLabel" name="velocityLabel">
         <property name="text">
          <string>0.000 mm/s</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="actualPosTextLabel">
         <property name="text">
          <string>实际位置:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLabel" name="actualPosLabel">
         <property name="text">
          <string>0.000 mm</string>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="QLabel" name="encoderPosTextLabel">
         <property name="text">
          <string>编码器位置:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="3">
        <widget class="QLabel" name="encoderPosLabel">
         <property name="text">
          <string>0</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>600</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
