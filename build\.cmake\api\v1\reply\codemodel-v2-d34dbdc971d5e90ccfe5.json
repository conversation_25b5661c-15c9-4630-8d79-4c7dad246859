{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AxisControlSystem", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "AxisControlSystem::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisControlSystem-Debug-256138e4c4b2b31cae29.json", "name": "AxisControlSystem", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisControlSystem_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisControlSystem_autogen-Debug-572425bec53ba4f9e96d.json", "name": "AxisControlSystem_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisControlSystem_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisControlSystem_autogen_timestamp_deps-Debug-dd4680e27e51749d41bc.json", "name": "AxisControlSystem_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AppData/Documents/GitHub/AxisControlSystem/build", "source": "D:/AppData/Documents/GitHub/AxisControlSystem"}, "version": {"major": 2, "minor": 7}}