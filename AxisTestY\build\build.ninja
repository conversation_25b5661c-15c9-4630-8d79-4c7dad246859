# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: AxisTestY
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build\
# =============================================================================
# Object build statements for EXECUTABLE target AxisTestY


#############################################
# Order-only phony target for AxisTestY

build cmake_object_order_depends_target_AxisTestY: phony || AxisTestY_autogen AxisTestY_autogen\mocs_compilation.cpp AxisTestY_autogen\timestamp AxisTestY_autogen_timestamp_deps

build CMakeFiles\AxisTestY.dir\AxisTestY_autogen\mocs_compilation.cpp.obj: CXX_COMPILER__AxisTestY_unscanned_Debug D$:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build\AxisTestY_autogen\mocs_compilation.cpp || cmake_object_order_depends_target_AxisTestY
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8
  INCLUDES = -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build\AxisTestY_autogen\include -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\..\shared\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtWidgets -external:IC:\Qt\6.8.0\msvc2022_64\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtCore -external:IC:\Qt\6.8.0\msvc2022_64\mkspecs\win32-msvc -external:IC:\Qt\6.8.0\msvc2022_64\include\QtGui -external:W0
  OBJECT_DIR = CMakeFiles\AxisTestY.dir
  OBJECT_FILE_DIR = CMakeFiles\AxisTestY.dir\AxisTestY_autogen
  TARGET_COMPILE_PDB = CMakeFiles\AxisTestY.dir\
  TARGET_PDB = AxisTestY.pdb

build CMakeFiles\AxisTestY.dir\src\AxisTestY.cpp.obj: CXX_COMPILER__AxisTestY_unscanned_Debug D$:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\src\AxisTestY.cpp || cmake_object_order_depends_target_AxisTestY
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8
  INCLUDES = -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build\AxisTestY_autogen\include -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\..\shared\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtWidgets -external:IC:\Qt\6.8.0\msvc2022_64\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtCore -external:IC:\Qt\6.8.0\msvc2022_64\mkspecs\win32-msvc -external:IC:\Qt\6.8.0\msvc2022_64\include\QtGui -external:W0
  OBJECT_DIR = CMakeFiles\AxisTestY.dir
  OBJECT_FILE_DIR = CMakeFiles\AxisTestY.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\AxisTestY.dir\
  TARGET_PDB = AxisTestY.pdb

build CMakeFiles\AxisTestY.dir\src\main.cpp.obj: CXX_COMPILER__AxisTestY_unscanned_Debug D$:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\src\main.cpp || cmake_object_order_depends_target_AxisTestY
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8
  INCLUDES = -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build\AxisTestY_autogen\include -ID:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\..\shared\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtWidgets -external:IC:\Qt\6.8.0\msvc2022_64\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtCore -external:IC:\Qt\6.8.0\msvc2022_64\mkspecs\win32-msvc -external:IC:\Qt\6.8.0\msvc2022_64\include\QtGui -external:W0
  OBJECT_DIR = CMakeFiles\AxisTestY.dir
  OBJECT_FILE_DIR = CMakeFiles\AxisTestY.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\AxisTestY.dir\
  TARGET_PDB = AxisTestY.pdb


# =============================================================================
# Link build statements for EXECUTABLE target AxisTestY


#############################################
# Link the executable AxisTestY.exe

build AxisTestY.exe: CXX_EXECUTABLE_LINKER__AxisTestY_Debug CMakeFiles\AxisTestY.dir\AxisTestY_autogen\mocs_compilation.cpp.obj CMakeFiles\AxisTestY.dir\src\AxisTestY.cpp.obj CMakeFiles\AxisTestY.dir\src\main.cpp.obj | C$:\Qt\6.8.0\msvc2022_64\lib\Qt6Widgetsd.lib D$:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\..\shared\lib\MCC6DLL_x64_Release.lib C$:\Qt\6.8.0\msvc2022_64\lib\Qt6Guid.lib C$:\Qt\6.8.0\msvc2022_64\lib\Qt6Cored.lib C$:\Qt\6.8.0\msvc2022_64\lib\Qt6EntryPointd.lib || AxisTestY_autogen AxisTestY_autogen_timestamp_deps
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:windows
  LINK_LIBRARIES = C:\Qt\6.8.0\msvc2022_64\lib\Qt6Widgetsd.lib  D:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\..\shared\lib\MCC6DLL_x64_Release.lib  C:\Qt\6.8.0\msvc2022_64\lib\Qt6Guid.lib  C:\Qt\6.8.0\msvc2022_64\lib\Qt6Cored.lib  mpr.lib  userenv.lib  C:\Qt\6.8.0\msvc2022_64\lib\Qt6EntryPointd.lib  shell32.lib  d3d11.lib  dxgi.lib  dxguid.lib  d3d12.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\AxisTestY.dir
  POST_BUILD = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build && C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/../shared/lib/MCC6DLL_x64_Release.dll D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build"
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\AxisTestY.dir\
  TARGET_FILE = AxisTestY.exe
  TARGET_IMPLIB = AxisTestY.lib
  TARGET_PDB = AxisTestY.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build && C:\Qt\Tools\CMake_64\bin\cmake-gui.exe -SD:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY -BD:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build && C:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -SD:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY -BD:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Utility command for AxisTestY_autogen_timestamp_deps

build AxisTestY_autogen_timestamp_deps: phony


#############################################
# Utility command for AxisTestY_autogen

build AxisTestY_autogen: phony CMakeFiles\AxisTestY_autogen AxisTestY_autogen\timestamp AxisTestY_autogen\mocs_compilation.cpp AxisTestY_autogen_timestamp_deps


#############################################
# Custom command for AxisTestY_autogen\timestamp

build AxisTestY_autogen\timestamp AxisTestY_autogen\mocs_compilation.cpp | ${cmake_ninja_workdir}AxisTestY_autogen\timestamp ${cmake_ninja_workdir}AxisTestY_autogen\mocs_compilation.cpp: CUSTOM_COMMAND C$:\Qt\6.8.0\msvc2022_64\bin\moc.exe C$:\Qt\6.8.0\msvc2022_64\bin\uic.exe || AxisTestY_autogen_timestamp_deps
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\build && C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build/CMakeFiles/AxisTestY_autogen.dir/AutogenInfo.json Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -E touch D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build/AxisTestY_autogen/timestamp && C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_transform_depfile Ninja gccdepfile D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build/AxisTestY_autogen/deps D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build/CMakeFiles/d/fcce3fa9446f9c73bd03a757ddfa26f7359ff81f02c8ebd4ba833f1f7b30b4ae.d"
  DESC = Automatic MOC and UIC for target AxisTestY
  depfile = CMakeFiles\d\fcce3fa9446f9c73bd03a757ddfa26f7359ff81f02c8ebd4ba833f1f7b30b4ae.d
  deps = gcc
  restat = 1


#############################################
# Phony custom command for CMakeFiles\AxisTestY_autogen

build CMakeFiles\AxisTestY_autogen | ${cmake_ninja_workdir}CMakeFiles\AxisTestY_autogen: phony AxisTestY_autogen\timestamp || AxisTestY_autogen_timestamp_deps

# =============================================================================
# Target aliases.

build AxisTestY: phony AxisTestY.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build

build all: phony AxisTestY.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\qt.toolchain.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckLibraryExists.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageMessage.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindThreads.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindVulkan.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\GNUInstallDirs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckCompilerFlag.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckFlagCommonConfig.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake D$:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake C$:\Qt\6.8.0\msvc2022_64\lib\cmake\Qt6\qt.toolchain.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckLibraryExists.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageMessage.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindThreads.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindVulkan.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\GNUInstallDirs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckCompilerFlag.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckFlagCommonConfig.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake D$:\AppData\Documents\GitHub\AxisControlSystem\AxisTestY\CMakeLists.txt: phony


#############################################
# Clean additional files.

build CMakeFiles\clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles\clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
