cmake_minimum_required(VERSION 3.5) # CMake install : https://cmake.org/download/
project(AxisControlSystem LANGUAGES CXX)

# 基本设置
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_PREFIX_PATH "C:/Qt/6.8.0/msvc2022_64") # Qt Kit Dir
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt组件
find_package(Qt6 COMPONENTS Widgets REQUIRED) # Qt COMPONENTS

# 指定 MSVC UTF-8编码 
add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")

# 添加共享库路径
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/shared/lib)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/shared/include)

# 构建AxisTestX
file(GLOB AXIS_TEST_X_SRCS
    "./AxisTestX/src/*.cpp"
    "./AxisTestX/src/*.h"
    "./AxisTestX/src/*.ui"
)
add_executable(AxisTestX WIN32 ${AXIS_TEST_X_SRCS})
target_link_libraries(AxisTestX PRIVATE
    Qt6::Widgets
    ${CMAKE_CURRENT_SOURCE_DIR}/shared/lib/MCC6DLL_x64_Release.lib
)
# 复制DLL到输出目录
add_custom_command(TARGET AxisTestX POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/shared/lib/MCC6DLL_x64_Release.dll"
    $<TARGET_FILE_DIR:AxisTestX>
)

# 构建AxisTestY
file(GLOB AXIS_TEST_Y_SRCS
    "./AxisTestY/src/*.cpp"
    "./AxisTestY/src/*.h"
    "./AxisTestY/src/*.ui"
)
add_executable(AxisTestY WIN32 ${AXIS_TEST_Y_SRCS})
target_link_libraries(AxisTestY PRIVATE
    Qt6::Widgets
    ${CMAKE_CURRENT_SOURCE_DIR}/shared/lib/MCC6DLL_x64_Release.lib
)
# 复制DLL到输出目录
add_custom_command(TARGET AxisTestY POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/shared/lib/MCC6DLL_x64_Release.dll"
    $<TARGET_FILE_DIR:AxisTestY>
)

# 构建AxisTestZ
file(GLOB AXIS_TEST_Z_SRCS
    "./AxisTestZ/src/*.cpp"
    "./AxisTestZ/src/*.h"
    "./AxisTestZ/src/*.ui"
)
add_executable(AxisTestZ WIN32 ${AXIS_TEST_Z_SRCS})
target_link_libraries(AxisTestZ PRIVATE
    Qt6::Widgets
    ${CMAKE_CURRENT_SOURCE_DIR}/shared/lib/MCC6DLL_x64_Release.lib
)
# 复制DLL到输出目录
add_custom_command(TARGET AxisTestZ POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/shared/lib/MCC6DLL_x64_Release.dll"
    $<TARGET_FILE_DIR:AxisTestZ>
)

# aux_source_directory(./src srcs)
# add_executable(${PROJECT_NAME}
#     WIN32 # If you need a terminal for debug, please comment this statement 
#     ${srcs} 
# ) 
# target_link_libraries(${PROJECT_NAME} PRIVATE Qt6::Widgets) # Qt6 Shared Library