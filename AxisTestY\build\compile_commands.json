[{"directory": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build", "command": "C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build\\AxisTestY_autogen\\include -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\..\\shared\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtWidgets -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtCore -external:IC:\\Qt\\6.8.0\\msvc2022_64\\mkspecs\\win32-msvc -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8 /FoCMakeFiles\\AxisTestY.dir\\AxisTestY_autogen\\mocs_compilation.cpp.obj /FdCMakeFiles\\AxisTestY.dir\\ /FS -c D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build\\AxisTestY_autogen\\mocs_compilation.cpp", "file": "D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build\\AxisTestY_autogen\\mocs_compilation.cpp", "output": "CMakeFiles\\AxisTestY.dir\\AxisTestY_autogen\\mocs_compilation.cpp.obj"}, {"directory": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build", "command": "C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build\\AxisTestY_autogen\\include -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\..\\shared\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtWidgets -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtCore -external:IC:\\Qt\\6.8.0\\msvc2022_64\\mkspecs\\win32-msvc -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8 /FoCMakeFiles\\AxisTestY.dir\\src\\AxisTestY.cpp.obj /FdCMakeFiles\\AxisTestY.dir\\ /FS -c D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\src\\AxisTestY.cpp", "file": "D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\src\\AxisTestY.cpp", "output": "CMakeFiles\\AxisTestY.dir\\src\\AxisTestY.cpp.obj"}, {"directory": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestY/build", "command": "C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\build\\AxisTestY_autogen\\include -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\..\\shared\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtWidgets -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtCore -external:IC:\\Qt\\6.8.0\\msvc2022_64\\mkspecs\\win32-msvc -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8 /FoCMakeFiles\\AxisTestY.dir\\src\\main.cpp.obj /FdCMakeFiles\\AxisTestY.dir\\ /FS -c D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\src\\main.cpp", "file": "D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\AxisTestY\\src\\main.cpp", "output": "CMakeFiles\\AxisTestY.dir\\src\\main.cpp.obj"}]