#pragma once

#include <QMainWindow>
#include <QTimer>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QRadioButton>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QMessageBox>
#include <QStatusBar>
#include <QMenuBar>
#include <QAction>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include "MCC6DLL.h"

class AxisTestZ : public QMainWindow
{
    Q_OBJECT

public:
    AxisTestZ(QWidget *parent = nullptr);
    ~AxisTestZ();

private slots:
    void onConnectClicked();
    void onDisconnectClicked();
    void onEnableAxisClicked();
    void onDisableAxisClicked();
    void onEmergencyStopClicked();
    void onStartMoveClicked();
    void onStopMoveClicked();
    void onHomeClicked();
    void onUpdateStatus();
    void onMoveTypeChanged();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupConnections();
    void updateConnectionStatus();
    void updateAxisStatus();
    void enableControls(bool enabled);
    bool validateMoveParameters();
    void showErrorMessage(const QString &message);
    void logMessage(const QString &message);

private:
    // UI Components
    QWidget *m_centralWidget;
    
    // Connection Group
    QGroupBox *m_connectionGroup;
    QPushButton *m_connectBtn;
    QPushButton *m_disconnectBtn;
    QComboBox *m_portCombo;
    QLabel *m_connectionStatus;
    
    // Axis Control Group
    QGroupBox *m_axisControlGroup;
    QPushButton *m_enableAxisBtn;
    QPushButton *m_disableAxisBtn;
    QPushButton *m_emergencyStopBtn;
    QLabel *m_axisStatus;
    
    // Position Display Group
    QGroupBox *m_positionGroup;
    QLabel *m_currentPosLabel;
    QLabel *m_actualPosLabel;
    QLabel *m_velocityLabel;
    QLabel *m_encoderPosLabel;
    
    // Movement Control Group
    QGroupBox *m_movementGroup;
    QRadioButton *m_absoluteRadio;
    QRadioButton *m_relativeRadio;
    QRadioButton *m_velocityRadio;
    QDoubleSpinBox *m_targetPosSpin;
    QDoubleSpinBox *m_initialVelSpin;
    QDoubleSpinBox *m_initialAccSpin;
    QDoubleSpinBox *m_maxAccSpin;
    QDoubleSpinBox *m_maxVelSpin;
    QPushButton *m_startMoveBtn;
    QPushButton *m_stopMoveBtn;
    QPushButton *m_homeBtn;
    
    // Status and Timer
    QTimer *m_statusTimer;
    QLabel *m_statusLabel;
    
    // Motion Control Card
    MoCtrCard *m_motionCard;
    bool m_isConnected;
    bool m_isAxisEnabled;
    
    // Axis Configuration
    static const int AXIS_ID = 2; // Z轴 = 2
};
