[{"directory": "D:/AppData/Documents/GitHub/AxisControlSystem/build", "command": "C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build\\AxisControlSystem_autogen\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtWidgets -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtCore -external:IC:\\Qt\\6.8.0\\msvc2022_64\\mkspecs\\win32-msvc -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8 /FoCMakeFiles\\AxisControlSystem.dir\\AxisControlSystem_autogen\\mocs_compilation.cpp.obj /FdCMakeFiles\\AxisControlSystem.dir\\ /FS -c D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build\\AxisControlSystem_autogen\\mocs_compilation.cpp", "file": "D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build\\AxisControlSystem_autogen\\mocs_compilation.cpp", "output": "CMakeFiles\\AxisControlSystem.dir\\AxisControlSystem_autogen\\mocs_compilation.cpp.obj"}, {"directory": "D:/AppData/Documents/GitHub/AxisControlSystem/build", "command": "C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build\\AxisControlSystem_autogen\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtWidgets -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtCore -external:IC:\\Qt\\6.8.0\\msvc2022_64\\mkspecs\\win32-msvc -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8 /FoCMakeFiles\\AxisControlSystem.dir\\src\\AxisControlSystem.cpp.obj /FdCMakeFiles\\AxisControlSystem.dir\\ /FS -c D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\src\\AxisControlSystem.cpp", "file": "D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\src\\AxisControlSystem.cpp", "output": "CMakeFiles\\AxisControlSystem.dir\\src\\AxisControlSystem.cpp.obj"}, {"directory": "D:/AppData/Documents/GitHub/AxisControlSystem/build", "command": "C:\\PROGRA~1\\MIB055~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem -ID:\\AppData\\Documents\\GitHub\\AxisControlSystem\\build\\AxisControlSystem_autogen\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtWidgets -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtCore -external:IC:\\Qt\\6.8.0\\msvc2022_64\\mkspecs\\win32-msvc -external:IC:\\Qt\\6.8.0\\msvc2022_64\\include\\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 -Zc:__cplusplus -permissive- -utf-8 /FoCMakeFiles\\AxisControlSystem.dir\\src\\main.cpp.obj /FdCMakeFiles\\AxisControlSystem.dir\\ /FS -c D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\src\\main.cpp", "file": "D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\src\\main.cpp", "output": "CMakeFiles\\AxisControlSystem.dir\\src\\main.cpp.obj"}]