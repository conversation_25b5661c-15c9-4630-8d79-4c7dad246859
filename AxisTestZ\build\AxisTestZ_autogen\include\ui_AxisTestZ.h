/********************************************************************************
** Form generated from reading UI file 'AxisTestZ.ui'
**
** Created by: Qt User Interface Compiler version 6.8.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_AXISTESTZ_H
#define UI_AXISTESTZ_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_AxisTestZ
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QGroupBox *connectionGroup;
    QHBoxLayout *connectionLayout;
    QLabel *portLabel;
    QComboBox *portCombo;
    QPushButton *connectBtn;
    QPushButton *disconnectBtn;
    QLabel *connectionStatus;
    QGroupBox *axisControlGroup;
    QHBoxLayout *axisControlLayout;
    QPushButton *enableAxisBtn;
    QPushButton *disableAxisBtn;
    QPushButton *emergencyStopBtn;
    QLabel *axisStatus;
    QGroupBox *positionGroup;
    QGridLayout *positionLayout;
    QLabel *currentPosTextLabel;
    QLabel *currentPosLabel;
    QLabel *velocityTextLabel;
    QLabel *velocityLabel;
    QLabel *actualPosTextLabel;
    QLabel *actualPosLabel;
    QLabel *encoderPosTextLabel;
    QLabel *encoderPosLabel;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *AxisTestZ)
    {
        if (AxisTestZ->objectName().isEmpty())
            AxisTestZ->setObjectName("AxisTestZ");
        AxisTestZ->resize(600, 500);
        centralwidget = new QWidget(AxisTestZ);
        centralwidget->setObjectName("centralwidget");
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName("verticalLayout");
        connectionGroup = new QGroupBox(centralwidget);
        connectionGroup->setObjectName("connectionGroup");
        connectionLayout = new QHBoxLayout(connectionGroup);
        connectionLayout->setObjectName("connectionLayout");
        portLabel = new QLabel(connectionGroup);
        portLabel->setObjectName("portLabel");

        connectionLayout->addWidget(portLabel);

        portCombo = new QComboBox(connectionGroup);
        portCombo->setObjectName("portCombo");

        connectionLayout->addWidget(portCombo);

        connectBtn = new QPushButton(connectionGroup);
        connectBtn->setObjectName("connectBtn");

        connectionLayout->addWidget(connectBtn);

        disconnectBtn = new QPushButton(connectionGroup);
        disconnectBtn->setObjectName("disconnectBtn");

        connectionLayout->addWidget(disconnectBtn);

        connectionStatus = new QLabel(connectionGroup);
        connectionStatus->setObjectName("connectionStatus");

        connectionLayout->addWidget(connectionStatus);


        verticalLayout->addWidget(connectionGroup);

        axisControlGroup = new QGroupBox(centralwidget);
        axisControlGroup->setObjectName("axisControlGroup");
        axisControlLayout = new QHBoxLayout(axisControlGroup);
        axisControlLayout->setObjectName("axisControlLayout");
        enableAxisBtn = new QPushButton(axisControlGroup);
        enableAxisBtn->setObjectName("enableAxisBtn");

        axisControlLayout->addWidget(enableAxisBtn);

        disableAxisBtn = new QPushButton(axisControlGroup);
        disableAxisBtn->setObjectName("disableAxisBtn");

        axisControlLayout->addWidget(disableAxisBtn);

        emergencyStopBtn = new QPushButton(axisControlGroup);
        emergencyStopBtn->setObjectName("emergencyStopBtn");

        axisControlLayout->addWidget(emergencyStopBtn);

        axisStatus = new QLabel(axisControlGroup);
        axisStatus->setObjectName("axisStatus");

        axisControlLayout->addWidget(axisStatus);


        verticalLayout->addWidget(axisControlGroup);

        positionGroup = new QGroupBox(centralwidget);
        positionGroup->setObjectName("positionGroup");
        positionLayout = new QGridLayout(positionGroup);
        positionLayout->setObjectName("positionLayout");
        currentPosTextLabel = new QLabel(positionGroup);
        currentPosTextLabel->setObjectName("currentPosTextLabel");

        positionLayout->addWidget(currentPosTextLabel, 0, 0, 1, 1);

        currentPosLabel = new QLabel(positionGroup);
        currentPosLabel->setObjectName("currentPosLabel");

        positionLayout->addWidget(currentPosLabel, 0, 1, 1, 1);

        velocityTextLabel = new QLabel(positionGroup);
        velocityTextLabel->setObjectName("velocityTextLabel");

        positionLayout->addWidget(velocityTextLabel, 0, 2, 1, 1);

        velocityLabel = new QLabel(positionGroup);
        velocityLabel->setObjectName("velocityLabel");

        positionLayout->addWidget(velocityLabel, 0, 3, 1, 1);

        actualPosTextLabel = new QLabel(positionGroup);
        actualPosTextLabel->setObjectName("actualPosTextLabel");

        positionLayout->addWidget(actualPosTextLabel, 1, 0, 1, 1);

        actualPosLabel = new QLabel(positionGroup);
        actualPosLabel->setObjectName("actualPosLabel");

        positionLayout->addWidget(actualPosLabel, 1, 1, 1, 1);

        encoderPosTextLabel = new QLabel(positionGroup);
        encoderPosTextLabel->setObjectName("encoderPosTextLabel");

        positionLayout->addWidget(encoderPosTextLabel, 1, 2, 1, 1);

        encoderPosLabel = new QLabel(positionGroup);
        encoderPosLabel->setObjectName("encoderPosLabel");

        positionLayout->addWidget(encoderPosLabel, 1, 3, 1, 1);


        verticalLayout->addWidget(positionGroup);

        AxisTestZ->setCentralWidget(centralwidget);
        menubar = new QMenuBar(AxisTestZ);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 600, 22));
        AxisTestZ->setMenuBar(menubar);
        statusbar = new QStatusBar(AxisTestZ);
        statusbar->setObjectName("statusbar");
        AxisTestZ->setStatusBar(statusbar);

        retranslateUi(AxisTestZ);

        QMetaObject::connectSlotsByName(AxisTestZ);
    } // setupUi

    void retranslateUi(QMainWindow *AxisTestZ)
    {
        AxisTestZ->setWindowTitle(QCoreApplication::translate("AxisTestZ", "Z\350\275\264\346\265\213\350\257\225\347\250\213\345\272\217", nullptr));
        connectionGroup->setTitle(QCoreApplication::translate("AxisTestZ", "\350\277\236\346\216\245\350\256\276\347\275\256", nullptr));
        portLabel->setText(QCoreApplication::translate("AxisTestZ", "\344\270\262\345\217\243:", nullptr));
        connectBtn->setText(QCoreApplication::translate("AxisTestZ", "\350\277\236\346\216\245", nullptr));
        disconnectBtn->setText(QCoreApplication::translate("AxisTestZ", "\346\226\255\345\274\200", nullptr));
        connectionStatus->setText(QCoreApplication::translate("AxisTestZ", "\346\234\252\350\277\236\346\216\245", nullptr));
        axisControlGroup->setTitle(QCoreApplication::translate("AxisTestZ", "Z\350\275\264\346\216\247\345\210\266", nullptr));
        enableAxisBtn->setText(QCoreApplication::translate("AxisTestZ", "\344\275\277\350\203\275\350\275\264", nullptr));
        disableAxisBtn->setText(QCoreApplication::translate("AxisTestZ", "\345\244\261\350\203\275\350\275\264", nullptr));
        emergencyStopBtn->setText(QCoreApplication::translate("AxisTestZ", "\346\200\245\345\201\234", nullptr));
        emergencyStopBtn->setStyleSheet(QCoreApplication::translate("AxisTestZ", "QPushButton { background-color: red; color: white; font-weight: bold; }", nullptr));
        axisStatus->setText(QCoreApplication::translate("AxisTestZ", "\347\212\266\346\200\201: \346\234\252\347\237\245", nullptr));
        positionGroup->setTitle(QCoreApplication::translate("AxisTestZ", "\344\275\215\347\275\256\344\277\241\346\201\257", nullptr));
        currentPosTextLabel->setText(QCoreApplication::translate("AxisTestZ", "\345\275\223\345\211\215\344\275\215\347\275\256:", nullptr));
        currentPosLabel->setText(QCoreApplication::translate("AxisTestZ", "0.000 mm", nullptr));
        velocityTextLabel->setText(QCoreApplication::translate("AxisTestZ", "\345\275\223\345\211\215\351\200\237\345\272\246:", nullptr));
        velocityLabel->setText(QCoreApplication::translate("AxisTestZ", "0.000 mm/s", nullptr));
        actualPosTextLabel->setText(QCoreApplication::translate("AxisTestZ", "\345\256\236\351\231\205\344\275\215\347\275\256:", nullptr));
        actualPosLabel->setText(QCoreApplication::translate("AxisTestZ", "0.000 mm", nullptr));
        encoderPosTextLabel->setText(QCoreApplication::translate("AxisTestZ", "\347\274\226\347\240\201\345\231\250\344\275\215\347\275\256:", nullptr));
        encoderPosLabel->setText(QCoreApplication::translate("AxisTestZ", "0", nullptr));
    } // retranslateUi

};

namespace Ui {
    class AxisTestZ: public Ui_AxisTestZ {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_AXISTESTZ_H
