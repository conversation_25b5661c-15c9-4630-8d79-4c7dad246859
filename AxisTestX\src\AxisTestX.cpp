#include "AxisTestX.h"

AxisTestX::AxisTestX(QWidget *parent)
    : QMainWindow(parent)
    , m_motionCard(nullptr)
    , m_isConnected(false)
    , m_isAxisEnabled(false)
{
    setupUI();
    setupMenuBar();
    setupStatusBar();
    setupConnections();
    
    // Initialize motion card
    m_motionCard = new MoCtrCard();
    
    // Setup status update timer
    m_statusTimer = new QTimer(this);
    connect(m_statusTimer, &QTimer::timeout, this, &AxisTestX::onUpdateStatus);
    
    // Initialize UI state
    enableControls(false);
    updateConnectionStatus();
}

AxisTestX::~AxisTestX()
{
    if (m_statusTimer) {
        m_statusTimer->stop();
    }
    
    if (m_motionCard && m_isConnected) {
        m_motionCard->MoCtrCard_Unload();
    }
    
    delete m_motionCard;
}

void AxisTestX::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    QVBoxLayout *mainLayout = new QVBoxLayout(m_centralWidget);
    
    // Connection Group
    m_connectionGroup = new QGroupBox("连接设置", this);
    QHBoxLayout *connLayout = new QHBoxLayout(m_connectionGroup);
    
    connLayout->addWidget(new QLabel("串口:", this));
    m_portCombo = new QComboBox(this);
    m_portCombo->addItems({"COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "COM10"});
    m_portCombo->setCurrentText("COM10");
    connLayout->addWidget(m_portCombo);
    
    m_connectBtn = new QPushButton("连接", this);
    m_disconnectBtn = new QPushButton("断开", this);
    m_connectionStatus = new QLabel("未连接", this);
    
    connLayout->addWidget(m_connectBtn);
    connLayout->addWidget(m_disconnectBtn);
    connLayout->addWidget(m_connectionStatus);
    connLayout->addStretch();
    
    mainLayout->addWidget(m_connectionGroup);
    
    // Axis Control Group
    m_axisControlGroup = new QGroupBox("X轴控制", this);
    QHBoxLayout *axisLayout = new QHBoxLayout(m_axisControlGroup);
    
    m_enableAxisBtn = new QPushButton("使能轴", this);
    m_disableAxisBtn = new QPushButton("失能轴", this);
    m_emergencyStopBtn = new QPushButton("急停", this);
    m_emergencyStopBtn->setStyleSheet("QPushButton { background-color: red; color: white; font-weight: bold; }");
    m_axisStatus = new QLabel("状态: 未知", this);
    
    axisLayout->addWidget(m_enableAxisBtn);
    axisLayout->addWidget(m_disableAxisBtn);
    axisLayout->addWidget(m_emergencyStopBtn);
    axisLayout->addWidget(m_axisStatus);
    axisLayout->addStretch();
    
    mainLayout->addWidget(m_axisControlGroup);
    
    // Position Display Group
    m_positionGroup = new QGroupBox("位置信息", this);
    QGridLayout *posLayout = new QGridLayout(m_positionGroup);
    
    posLayout->addWidget(new QLabel("当前位置:", this), 0, 0);
    m_currentPosLabel = new QLabel("0.000 mm", this);
    posLayout->addWidget(m_currentPosLabel, 0, 1);
    
    posLayout->addWidget(new QLabel("当前速度:", this), 0, 2);
    m_velocityLabel = new QLabel("0.000 mm/s", this);
    posLayout->addWidget(m_velocityLabel, 0, 3);
    
    posLayout->addWidget(new QLabel("实际位置:", this), 1, 0);
    m_actualPosLabel = new QLabel("0.000 mm", this);
    posLayout->addWidget(m_actualPosLabel, 1, 1);
    
    posLayout->addWidget(new QLabel("编码器位置:", this), 1, 2);
    m_encoderPosLabel = new QLabel("0", this);
    posLayout->addWidget(m_encoderPosLabel, 1, 3);
    
    mainLayout->addWidget(m_positionGroup);
    
    // Movement Control Group
    m_movementGroup = new QGroupBox("运动控制", this);
    QVBoxLayout *moveLayout = new QVBoxLayout(m_movementGroup);
    
    // Movement type selection
    QHBoxLayout *typeLayout = new QHBoxLayout();
    m_absoluteRadio = new QRadioButton("绝对运动", this);
    m_relativeRadio = new QRadioButton("相对运动", this);
    m_velocityRadio = new QRadioButton("速度运动", this);
    m_absoluteRadio->setChecked(true);
    
    typeLayout->addWidget(m_absoluteRadio);
    typeLayout->addWidget(m_relativeRadio);
    typeLayout->addWidget(m_velocityRadio);
    typeLayout->addStretch();
    moveLayout->addLayout(typeLayout);
    
    // Movement parameters
    QGridLayout *paramLayout = new QGridLayout();
    
    paramLayout->addWidget(new QLabel("目标位置 (mm):", this), 0, 0);
    m_targetPosSpin = new QDoubleSpinBox(this);
    m_targetPosSpin->setRange(-1000.0, 1000.0);
    m_targetPosSpin->setDecimals(3);
    paramLayout->addWidget(m_targetPosSpin, 0, 1);
    
    paramLayout->addWidget(new QLabel("初速度 (mm/s):", this), 0, 2);
    m_initialVelSpin = new QDoubleSpinBox(this);
    m_initialVelSpin->setRange(0.0, 100.0);
    m_initialVelSpin->setDecimals(3);
    m_initialVelSpin->setValue(1.0);
    paramLayout->addWidget(m_initialVelSpin, 0, 3);
    
    paramLayout->addWidget(new QLabel("初始加速度 (mm/s²):", this), 1, 0);
    m_initialAccSpin = new QDoubleSpinBox(this);
    m_initialAccSpin->setRange(0.0, 1000.0);
    m_initialAccSpin->setDecimals(3);
    m_initialAccSpin->setValue(10.0);
    paramLayout->addWidget(m_initialAccSpin, 1, 1);
    
    paramLayout->addWidget(new QLabel("最大加速度 (mm/s²):", this), 1, 2);
    m_maxAccSpin = new QDoubleSpinBox(this);
    m_maxAccSpin->setRange(0.0, 1000.0);
    m_maxAccSpin->setDecimals(3);
    m_maxAccSpin->setValue(50.0);
    paramLayout->addWidget(m_maxAccSpin, 1, 3);
    
    paramLayout->addWidget(new QLabel("最大速度 (mm/s):", this), 2, 0);
    m_maxVelSpin = new QDoubleSpinBox(this);
    m_maxVelSpin->setRange(0.0, 100.0);
    m_maxVelSpin->setDecimals(3);
    m_maxVelSpin->setValue(10.0);
    paramLayout->addWidget(m_maxVelSpin, 2, 1);
    
    moveLayout->addLayout(paramLayout);
    
    // Movement control buttons
    QHBoxLayout *btnLayout = new QHBoxLayout();
    m_startMoveBtn = new QPushButton("开始运动", this);
    m_stopMoveBtn = new QPushButton("停止运动", this);
    m_homeBtn = new QPushButton("回零", this);
    
    btnLayout->addWidget(m_startMoveBtn);
    btnLayout->addWidget(m_stopMoveBtn);
    btnLayout->addWidget(m_homeBtn);
    btnLayout->addStretch();
    moveLayout->addLayout(btnLayout);
    
    mainLayout->addWidget(m_movementGroup);
    mainLayout->addStretch();
}

void AxisTestX::setupMenuBar()
{
    QMenuBar *menuBar = this->menuBar();
    
    QMenu *fileMenu = menuBar->addMenu("文件");
    QAction *exitAction = fileMenu->addAction("退出");
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    QMenu *helpMenu = menuBar->addMenu("帮助");
    QAction *aboutAction = helpMenu->addAction("关于");
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "关于", "X轴测试程序 v1.0\n用于测试微纳滑台X轴控制功能");
    });
}

void AxisTestX::setupStatusBar()
{
    m_statusLabel = new QLabel("就绪", this);
    statusBar()->addWidget(m_statusLabel);
}

void AxisTestX::setupConnections()
{
    connect(m_connectBtn, &QPushButton::clicked, this, &AxisTestX::onConnectClicked);
    connect(m_disconnectBtn, &QPushButton::clicked, this, &AxisTestX::onDisconnectClicked);
    connect(m_enableAxisBtn, &QPushButton::clicked, this, &AxisTestX::onEnableAxisClicked);
    connect(m_disableAxisBtn, &QPushButton::clicked, this, &AxisTestX::onDisableAxisClicked);
    connect(m_emergencyStopBtn, &QPushButton::clicked, this, &AxisTestX::onEmergencyStopClicked);
    connect(m_startMoveBtn, &QPushButton::clicked, this, &AxisTestX::onStartMoveClicked);
    connect(m_stopMoveBtn, &QPushButton::clicked, this, &AxisTestX::onStopMoveClicked);
    connect(m_homeBtn, &QPushButton::clicked, this, &AxisTestX::onHomeClicked);
    
    connect(m_absoluteRadio, &QRadioButton::toggled, this, &AxisTestX::onMoveTypeChanged);
    connect(m_relativeRadio, &QRadioButton::toggled, this, &AxisTestX::onMoveTypeChanged);
    connect(m_velocityRadio, &QRadioButton::toggled, this, &AxisTestX::onMoveTypeChanged);
}

void AxisTestX::onConnectClicked()
{
    if (m_motionCard) {
        QString portText = m_portCombo->currentText();
        int portNum = portText.mid(3).toInt(); // Extract number from "COMx"

        McCard_UINT16 result = m_motionCard->MoCtrCard_Initial(portNum);
        if (result == funResOk) {
            m_isConnected = true;
            updateConnectionStatus();
            enableControls(true);

            // Start status update timer
            m_statusTimer->start(200); // Update every 200ms

            logMessage("成功连接到 " + portText);
        } else {
            showErrorMessage("连接失败，错误码: " + QString::number(result));
        }
    }
}

void AxisTestX::onDisconnectClicked()
{
    if (m_motionCard && m_isConnected) {
        m_statusTimer->stop();

        McCard_UINT16 result = m_motionCard->MoCtrCard_Unload();
        m_isConnected = false;
        m_isAxisEnabled = false;

        updateConnectionStatus();
        enableControls(false);

        logMessage("已断开连接");
    }
}

void AxisTestX::onEnableAxisClicked()
{
    if (!m_isConnected) {
        showErrorMessage("请先连接设备");
        return;
    }

    // Note: MCC6DLL may not have explicit axis enable function
    // This is a placeholder for axis enabling logic
    m_isAxisEnabled = true;
    updateAxisStatus();
    logMessage("X轴已使能");
}

void AxisTestX::onDisableAxisClicked()
{
    if (!m_isConnected) {
        showErrorMessage("请先连接设备");
        return;
    }

    // Stop any current movement first
    if (m_motionCard) {
        m_motionCard->MoCtrCard_EmergencyStopAxisMov(AXIS_ID);
    }

    m_isAxisEnabled = false;
    updateAxisStatus();
    logMessage("X轴已失能");
}

void AxisTestX::onEmergencyStopClicked()
{
    if (m_motionCard && m_isConnected) {
        McCard_UINT16 result = m_motionCard->MoCtrCard_EmergencyStopAxisMov(AXIS_ID);
        if (result == funResOk) {
            logMessage("X轴急停执行成功");
        } else {
            showErrorMessage("急停执行失败，错误码: " + QString::number(result));
        }
    }
}

void AxisTestX::onStartMoveClicked()
{
    if (!m_isConnected || !m_isAxisEnabled) {
        showErrorMessage("请先连接设备并使能轴");
        return;
    }

    if (!validateMoveParameters()) {
        return;
    }

    if (!m_motionCard) {
        showErrorMessage("运动控制卡未初始化");
        return;
    }

    McCard_UINT16 result = funResErr;
    float targetPos = m_targetPosSpin->value();
    float velocity = m_initialVelSpin->value();
    float acceleration = m_initialAccSpin->value();

    if (m_absoluteRadio->isChecked()) {
        // Absolute movement
        result = m_motionCard->MoCtrCard_MCrlAxisAbsMove(AXIS_ID, targetPos, velocity, acceleration);
        logMessage(QString("开始绝对运动到位置: %1 mm").arg(targetPos));
    } else if (m_relativeRadio->isChecked()) {
        // Relative movement
        result = m_motionCard->MoCtrCard_MCrlAxisRelMove(AXIS_ID, targetPos, velocity, acceleration);
        logMessage(QString("开始相对运动距离: %1 mm").arg(targetPos));
    } else if (m_velocityRadio->isChecked()) {
        // Velocity movement
        result = m_motionCard->MoCtrCard_MCrlAxisMoveAtSpd(AXIS_ID, velocity, acceleration);
        logMessage(QString("开始速度运动，速度: %1 mm/s").arg(velocity));
    }

    if (result != funResOk) {
        showErrorMessage("运动启动失败，错误码: " + QString::number(result));
    }
}

void AxisTestX::onStopMoveClicked()
{
    if (m_motionCard && m_isConnected) {
        float deceleration = m_initialAccSpin->value();
        McCard_UINT16 result = m_motionCard->MoCtrCard_StopAxisMov(AXIS_ID, deceleration);

        if (result == funResOk) {
            logMessage("X轴停止运动");
        } else {
            showErrorMessage("停止运动失败，错误码: " + QString::number(result));
        }
    }
}

void AxisTestX::onHomeClicked()
{
    if (!m_isConnected || !m_isAxisEnabled) {
        showErrorMessage("请先连接设备并使能轴");
        return;
    }

    if (m_motionCard) {
        float homeVelocity = m_initialVelSpin->value();
        float acceleration = m_initialAccSpin->value();

        McCard_UINT16 result = m_motionCard->MoCtrCard_SeekZero(AXIS_ID, homeVelocity, acceleration);

        if (result == funResOk) {
            logMessage("开始回零操作");
        } else {
            showErrorMessage("回零操作失败，错误码: " + QString::number(result));
        }
    }
}

void AxisTestX::onUpdateStatus()
{
    if (!m_motionCard || !m_isConnected) {
        return;
    }

    // Update position information
    McCard_FP32 position[6] = {0};
    McCard_FP32 actualPos[6] = {0};
    McCard_FP32 velocity[6] = {0};
    McCard_INT32 encoderPos[6] = {0};

    // Get current position
    if (m_motionCard->MoCtrCard_GetAxisPos(AXIS_ID, position) == funResOk) {
        m_currentPosLabel->setText(QString("%1 mm").arg(position[AXIS_ID], 0, 'f', 3));
    }

    // Get actual position
    if (m_motionCard->MoCtrCard_GetAxisActualPos(AXIS_ID, actualPos) == funResOk) {
        m_actualPosLabel->setText(QString("%1 mm").arg(actualPos[AXIS_ID], 0, 'f', 3));
    }

    // Get velocity
    if (m_motionCard->MoCtrCard_GetAxisSpd(AXIS_ID, velocity) == funResOk) {
        m_velocityLabel->setText(QString("%1 mm/s").arg(velocity[AXIS_ID], 0, 'f', 3));
    }

    // Get encoder position
    if (m_motionCard->MoCtrCard_GetEncoderVal(AXIS_ID, encoderPos) == funResOk) {
        m_encoderPosLabel->setText(QString::number(encoderPos[AXIS_ID]));
    }

    // Update axis status
    updateAxisStatus();
}

void AxisTestX::onMoveTypeChanged()
{
    if (m_velocityRadio->isChecked()) {
        m_targetPosSpin->setEnabled(false);
        m_targetPosSpin->setSuffix(" mm/s");
        m_targetPosSpin->setRange(-100.0, 100.0);
    } else {
        m_targetPosSpin->setEnabled(true);
        m_targetPosSpin->setSuffix(" mm");
        m_targetPosSpin->setRange(-1000.0, 1000.0);
    }
}

void AxisTestX::updateConnectionStatus()
{
    if (m_isConnected) {
        m_connectionStatus->setText("已连接");
        m_connectionStatus->setStyleSheet("color: green; font-weight: bold;");
        m_connectBtn->setEnabled(false);
        m_disconnectBtn->setEnabled(true);
    } else {
        m_connectionStatus->setText("未连接");
        m_connectionStatus->setStyleSheet("color: red; font-weight: bold;");
        m_connectBtn->setEnabled(true);
        m_disconnectBtn->setEnabled(false);
    }
}

void AxisTestX::updateAxisStatus()
{
    if (!m_isConnected) {
        m_axisStatus->setText("状态: 未连接");
        m_axisStatus->setStyleSheet("color: gray;");
        return;
    }

    if (m_motionCard) {
        McCard_INT32 runState[6] = {0};
        if (m_motionCard->MoCtrCard_IsAxisRunning(AXIS_ID, runState) == funResOk) {
            if (runState[AXIS_ID] == 1) {
                m_axisStatus->setText("状态: 运动中");
                m_axisStatus->setStyleSheet("color: blue; font-weight: bold;");
            } else {
                if (m_isAxisEnabled) {
                    m_axisStatus->setText("状态: 已使能");
                    m_axisStatus->setStyleSheet("color: green; font-weight: bold;");
                } else {
                    m_axisStatus->setText("状态: 已停止");
                    m_axisStatus->setStyleSheet("color: orange;");
                }
            }
        }
    }
}

void AxisTestX::enableControls(bool enabled)
{
    m_axisControlGroup->setEnabled(enabled);
    m_movementGroup->setEnabled(enabled);
}

bool AxisTestX::validateMoveParameters()
{
    if (m_targetPosSpin->value() == 0 && !m_velocityRadio->isChecked()) {
        showErrorMessage("请设置有效的目标位置");
        return false;
    }

    if (m_initialVelSpin->value() <= 0) {
        showErrorMessage("初速度必须大于0");
        return false;
    }

    if (m_initialAccSpin->value() <= 0) {
        showErrorMessage("初始加速度必须大于0");
        return false;
    }

    return true;
}

void AxisTestX::showErrorMessage(const QString &message)
{
    QMessageBox::warning(this, "错误", message);
    logMessage("错误: " + message);
}

void AxisTestX::logMessage(const QString &message)
{
    m_statusLabel->setText(message);
    // Could also add to a log file or console here
}
