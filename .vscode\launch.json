{"version": "0.2.0", "configurations": [{"name": "QtBuild", "type": "cppvsdbg", "request": "launch", "program": "${command:cmake.launchTargetPath}", "args": [], "stopAtEntry": false, "cwd": "${workspaceRoot}", "environment": [{"name": "PATH", "value": "C:/Qt/6.8.0/msvc2022_64/bin"}], "console": "integratedTerminal", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\a0be316a26041902ea14dd157dde255d\\tonka3000.qtvsctools\\qt.natvis.xml"}]}