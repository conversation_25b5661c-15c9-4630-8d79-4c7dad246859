/********************************************************************************
** Form generated from reading UI file 'AxisControlSystem.ui'
**
** Created by: Qt User Interface Compiler version 6.8.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_AXISCONTROLSYSTEM_H
#define UI_AXISCONTROLSYSTEM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_AxisControlSystem
{
public:
    QWidget *centralwidget;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *AxisControlSystem)
    {
        if (AxisControlSystem->objectName().isEmpty())
            AxisControlSystem->setObjectName("AxisControlSystem");
        AxisControlSystem->resize(800, 600);
        centralwidget = new QWidget(AxisControlSystem);
        centralwidget->setObjectName("centralwidget");
        AxisControlSystem->setCentralWidget(centralwidget);
        menubar = new QMenuBar(AxisControlSystem);
        menubar->setObjectName("menubar");
        AxisControlSystem->setMenuBar(menubar);
        statusbar = new QStatusBar(AxisControlSystem);
        statusbar->setObjectName("statusbar");
        AxisControlSystem->setStatusBar(statusbar);

        retranslateUi(AxisControlSystem);

        QMetaObject::connectSlotsByName(AxisControlSystem);
    } // setupUi

    void retranslateUi(QMainWindow *AxisControlSystem)
    {
        AxisControlSystem->setWindowTitle(QCoreApplication::translate("AxisControlSystem", "AxisControlSystem", nullptr));
    } // retranslateUi

};

namespace Ui {
    class AxisControlSystem: public Ui_AxisControlSystem {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_AXISCONTROLSYSTEM_H
