{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "AxisTestX", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "AxisTestX::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestX-Debug-ab105ab8cd6dedf80038.json", "name": "AxisTestX", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisTestX_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestX_autogen-Debug-b319bc53d125b274ebb8.json", "name": "AxisTestX_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "AxisTestX_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-AxisTestX_autogen_timestamp_deps-Debug-febf5164dc2655fadeb0.json", "name": "AxisTestX_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestX/build", "source": "D:/AppData/Documents/GitHub/AxisControlSystem/AxisTestX"}, "version": {"major": 2, "minor": 7}}