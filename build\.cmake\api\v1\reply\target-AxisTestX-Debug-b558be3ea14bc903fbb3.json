{"artifacts": [{"path": "AxisTestX.exe"}, {"path": "AxisTestX.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "add_compile_options", "include_directories"], "files": ["CMakeLists.txt", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 26, "parent": 0}, {"command": 1, "file": 0, "line": 21, "parent": 0}, {"command": 2, "file": 0, "line": 27, "parent": 0}, {"command": 5, "file": 0, "line": 14, "parent": 0}, {"file": 3, "parent": 4}, {"command": 5, "file": 3, "line": 181, "parent": 5}, {"file": 2, "parent": 6}, {"command": 4, "file": 2, "line": 55, "parent": 7}, {"file": 1, "parent": 8}, {"command": 3, "file": 1, "line": 61, "parent": 9}, {"command": 4, "file": 2, "line": 43, "parent": 7}, {"file": 8, "parent": 11}, {"command": 7, "file": 8, "line": 43, "parent": 12}, {"command": 6, "file": 7, "line": 143, "parent": 13}, {"command": 5, "file": 6, "line": 76, "parent": 14}, {"file": 5, "parent": 15}, {"command": 4, "file": 5, "line": 57, "parent": 16}, {"file": 4, "parent": 17}, {"command": 3, "file": 4, "line": 61, "parent": 18}, {"command": 4, "file": 5, "line": 45, "parent": 16}, {"file": 11, "parent": 20}, {"command": 7, "file": 11, "line": 44, "parent": 21}, {"command": 6, "file": 7, "line": 143, "parent": 22}, {"command": 5, "file": 6, "line": 76, "parent": 23}, {"file": 10, "parent": 24}, {"command": 4, "file": 10, "line": 55, "parent": 25}, {"file": 9, "parent": 26}, {"command": 3, "file": 9, "line": 61, "parent": 27}, {"command": 3, "file": 9, "line": 83, "parent": 27}, {"command": 6, "file": 7, "line": 143, "parent": 13}, {"command": 5, "file": 6, "line": 76, "parent": 30}, {"file": 13, "parent": 31}, {"command": 4, "file": 13, "line": 55, "parent": 32}, {"file": 12, "parent": 33}, {"command": 3, "file": 12, "line": 61, "parent": 34}, {"command": 8, "file": 0, "line": 18, "parent": 0}, {"command": 9, "file": 0, "line": 22, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17"}, {"backtrace": 36, "fragment": "/utf-8"}, {"backtrace": 3, "fragment": "-Zc:__cplusplus"}, {"backtrace": 3, "fragment": "-permissive-"}, {"backtrace": 3, "fragment": "-utf-8"}], "defines": [{"backtrace": 3, "define": "QT_CORE_LIB"}, {"backtrace": 3, "define": "QT_GUI_LIB"}, {"backtrace": 3, "define": "QT_WIDGETS_LIB"}, {"backtrace": 3, "define": "UNICODE"}, {"backtrace": 3, "define": "WIN32"}, {"backtrace": 3, "define": "WIN64"}, {"backtrace": 3, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 3, "define": "_UNICODE"}, {"backtrace": 3, "define": "_WIN64"}], "includes": [{"path": "D:/AppData/Documents/GitHub/AxisControlSystem/build"}, {"path": "D:/AppData/Documents/GitHub/AxisControlSystem"}, {"backtrace": 0, "path": "D:/AppData/Documents/GitHub/AxisControlSystem/build/AxisTestX_autogen/include"}, {"backtrace": 37, "path": "D:/AppData/Documents/GitHub/AxisControlSystem/shared/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.8.0/msvc2022_64/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.8.0/msvc2022_64/include/QtCore"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.8.0/msvc2022_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "17"}, "sourceIndexes": [0, 1, 3]}], "dependencies": [{"id": "AxisTestX_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "AxisTestX_autogen::@6890427a1f51a3e7e1df"}], "id": "AxisTestX::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 2, "fragment": "-LIBPATH:D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\shared\\lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "C:\\Qt\\6.8.0\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\AppData\\Documents\\GitHub\\AxisControlSystem\\shared\\lib\\MCC6DLL_x64_Release.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\Qt\\6.8.0\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\Qt\\6.8.0\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 19, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 19, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "C:\\Qt\\6.8.0\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "AxisTestX", "nameOnDisk": "AxisTestX.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 3]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}, {"name": "", "sourceIndexes": [4]}, {"name": "CMake Rules", "sourceIndexes": [5]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/AxisTestX_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "AxisTestX/src/AxisTestX.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "AxisTestX/src/AxisTestX.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "AxisTestX/src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/AxisTestX_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/AxisTestX_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}